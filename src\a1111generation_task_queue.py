﻿import asyncio


class TaskQueue:
    def __init__(self):
        self.queue = []
        self.current_task = None
        self.is_processing = False

    def add_task(self, task):
        self.queue.append(task)
        task.queue_position = len(self.queue)
        task.update_status()

    def remove_task(self, task):
        if task in self.queue:
            self.queue.remove(task)
            # Actualizar posiciones de las tareas restantes
            for i, t in enumerate(self.queue):
                t.queue_position = i + 1
                t.update_status()

    def get_next_task(self):
        if self.queue:
            return self.queue.pop(0)
        return None

    async def process_queue(self):
        while True:
            if not self.is_processing and self.queue:
                self.is_processing = True
                self.current_task = self.get_next_task()

                # Actualizar posiciones de las tareas restantes
                for i, task in enumerate(self.queue):
                    task.queue_position = i + 1
                    task.update_status()

                await self.current_task.start()
                self.current_task = None
                self.is_processing = False

            await asyncio.sleep(0.1)
