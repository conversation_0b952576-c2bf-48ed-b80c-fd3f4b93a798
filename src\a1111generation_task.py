import flet as ft
from a1111client import A1111Client
import asyncio
import aiohttp
import os
import base64
from PIL import Image
from io import BytesIO
from a1111image_viewer import ImageViewer

test_url = "https://rndvf-3-135-152-169.a.free.pinggy.link"



app_data_path = os.getenv("FLET_APP_STORAGE_DATA")
app_temp_path = os.getenv("FLET_APP_STORAGE_TEMP")

class GenerationTaskTest:
    def __init__(self, page: ft.Page, parent_container, label, payload, task_queue, task_type="txt2img", app_instance=None):
        self.page = page
        self.parent_container = parent_container
        self.label = label
        self.payload = payload
        self.task_queue = task_queue
        self.task_type = task_type  # "txt2img", "hr_upscale", "repeat", "upscale"
        self.working = False
        self.completed = False  # Nueva propiedad para identificar tareas completadas
        self.has_error = False  # Nueva propiedad para identificar tareas con error
        self.queue_position = 0
        self.content = self.build()
        self.progress_ring = None
        self.app_instance = app_instance  # Reference to main app for gallery refresh

    def update_status(self):
        if self.queue_position == 1:
            self.status_text.value = "Próximo en cola"
            self.cancel_button.visible = True  # Permitir cancelar el próximo en cola
        elif self.queue_position > 1:
            self.status_text.value = f"En cola (#{self.queue_position})"
            self.cancel_button.visible = True
        else:
            self.status_text.value = "Procesando..."
            self.cancel_button.visible = False  # Solo ocultar para la tarea activa

        self.page.update()

    async def remove_from_parent(self):
        print(f"Removing task: {self.label}")
        self.working = False
        if self.content in self.parent_container.controls:
            self.parent_container.controls.remove(self.content)
            self.page.update()
            print(f"Successfully removed: {self.label}")
        else:
            print(f"Content not found in parent container: {self.label}")

    async def start_handler(self):
        """Ejecutar la generación de imagen"""
        try:
            # Crear payload

            # Crear cliente
            client = A1111Client(test_url)

            # Iniciar monitoreo de progreso en paralelo
            progress_task = asyncio.create_task(self.monitor_progress())

            # Generar imagen usando el tipo de tarea correcto
            generation_task = asyncio.create_task(client.generate_image(self.payload, self.task_type))

            # Esperar a que termine la generación
            result = await generation_task

            # Detener el monitoreo de progreso
            self.monitoring_progress = False
            progress_task.cancel()

            # Procesar resultado si contiene imágenes
            if (
                isinstance(result, dict)
                and "images" in result
                and len(result["images"]) > 0
            ):
                # Guardar referencia para metadatos
                self.current_generation_images = result["images"]

                # Procesar todas las imágenes en paralelo con el resultado completo
                await self.save_and_display_all_images(result["images"], result)
            else:
                # Si no hay imagen, mostrar error en la UI
                await self.show_error("No se pudo generar la imagen")

        except Exception as e:
            # Detener monitoreo en caso de error
            self.monitoring_progress = False
            # Mostrar error en la UI
            await self.show_error(f"Error: {str(e)}")

        # Completar tarea
        if self.working:
            await self.complete_task()

    async def monitor_progress(self):
        """Monitorear el progreso de generación cada 0.1 segundos"""
        self.monitoring_progress = True
        progress_url = f"{test_url}/sdapi/v1/progress"

        async with aiohttp.ClientSession() as session:
            while self.monitoring_progress and self.working:
                try:
                    async with session.get(
                        f"{progress_url}?skip_current_image=false"
                    ) as response:
                        if response.status == 200:
                            progress_data = await response.json()
                            await self.update_progress_from_api(progress_data)

                    await asyncio.sleep(0.1)  # Esperar 0.1 segundos

                except Exception as e:
                    # Si hay error en el progreso, continuar sin mostrar error
                    # (la generación principal sigue funcionando)
                    await asyncio.sleep(0.1)

    async def update_progress_from_api(self, progress_data):
        """Actualizar la UI con datos de progreso de la API"""
        try:
            progress = progress_data.get("progress", 0)
            state = progress_data.get("state", {})

            # Actualizar el anillo de progreso
            if hasattr(self, "progress_ring") and self.progress_ring:
                self.progress_ring.value = progress

                # Actualizar texto con información adicional
                sampling_step = state.get("sampling_step", 0)
                sampling_steps = state.get("sampling_steps", 20)
                eta_relative = progress_data.get("eta_relative", 0)

                # Actualizar el texto de estado
                if hasattr(self, "status_text"):
                    self.status_text.value = f"Generando... {int(progress * 100)}% ({sampling_step}/{sampling_steps}) ETA: {int(eta_relative)}s"

                self.page.update()

        except Exception as e:
            # Error silencioso en actualización de progreso
            pass

    async def save_and_display_all_images(self, base64_images, generation_result=None):
        """Guardar y mostrar todas las imágenes generadas en paralelo"""
        try:
            # Crear directorio de salida
            output_dir = os.path.join(app_data_path, "outputs", "txt2img")
            os.makedirs(output_dir, exist_ok=True)

            # Procesar todas las imágenes en paralelo
            tasks = []
            for i, base64_image in enumerate(base64_images):
                task = asyncio.create_task(
                    self.save_single_image(
                        base64_image, i, output_dir, generation_result
                    )
                )
                tasks.append(task)

            # Esperar a que todas las imágenes se procesen
            image_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Filtrar resultados exitosos
            successful_images = [
                result
                for result in image_results
                if isinstance(result, tuple) and len(result) == 2
            ]

            if successful_images:
                # Mostrar todas las imágenes en la UI
                await self.show_generated_images(successful_images)

                # Refresh gallery if app instance is available
                if self.app_instance and hasattr(self.app_instance, 'refresh_gallery'):
                    self.app_instance.refresh_gallery()
            else:
                await self.show_error("No se pudieron procesar las imágenes")

        except Exception as e:
            await self.show_error(f"Error procesando imágenes: {str(e)}")

    async def save_single_image(
        self, base64_image, index, output_dir, generation_result=None
    ):
        """Guardar una sola imagen y su JSON correspondiente"""
        try:
            # Decodificar imagen base64
            image_data = base64.b64decode(base64_image)
            image = Image.open(BytesIO(image_data))

            # Generar nombre único para la imagen
            import time
            import re

            timestamp = int(time.time())
            # Limpiar el label de caracteres inválidos para nombres de archivo
            clean_label = re.sub(r'[<>:"/\\|?*]', '_', self.label)
            clean_label = clean_label.replace(' ', '_')
            base_filename = f"{clean_label}_{timestamp}_{index}"

            # Rutas para imagen y JSON
            image_path = os.path.join(output_dir, f"{base_filename}.png")
            json_path = os.path.join(output_dir, f"{base_filename}.json")

            # Guardar imagen
            image.save(image_path)

            # Guardar JSON con información de generación
            if generation_result:
                import json

                # Crear estructura de datos para el JSON
                generation_data = {
                    "image_filename": f"{base_filename}.png",
                    "image_index": index,
                    "generation_timestamp": timestamp,
                    "task_label": self.label,
                    "parameters": generation_result.get("parameters", {}),
                    "info": generation_result.get("info", ""),
                    "generation_metadata": {
                        "total_images": len(self.current_generation_images)
                        if hasattr(self, "current_generation_images")
                        else 1,
                        "image_number": index + 1,
                    },
                }

                # Guardar JSON
                with open(json_path, "w", encoding="utf-8") as f:
                    json.dump(generation_data, f, indent=2, ensure_ascii=False)

            return (image_path, base64_image)

        except Exception as e:
            # Retornar excepción para que sea filtrada
            return e

    async def save_and_display_image(self, base64_image, generation_result=None):
        """Método legacy - mantener para compatibilidad"""
        await self.save_and_display_all_images([base64_image], generation_result)

    async def show_generated_images(self, image_results):
        """Mostrar múltiples imágenes generadas en la UI"""
        # Crear widgets de imagen para cada resultado
        image_widgets = []

        def dummy_callback(label, payload, task_type="txt2img"):
            pass

        for i, (image_path, base64_data) in enumerate(image_results):
            image_widget = ImageViewer(self.page, image_path, dummy_callback).content
            image_widgets.append(image_widget)

        # Organizar imágenes en filas (máximo 2 por fila)
        image_rows = []
        for i in range(0, len(image_widgets), 2):
            row_images = image_widgets[i : i + 2]
            image_row = ft.Row(
                controls=row_images, alignment=ft.MainAxisAlignment.CENTER, spacing=10
            )
            image_rows.append(image_row)

        # Marcar tarea como completada
        self.completed = True

        # Actualizar contenido para mostrar todas las imágenes
        self.content.content = ft.Column(
            controls=[
                ft.Text(self.label, weight=ft.FontWeight.BOLD),
                ft.Text(
                    f"✅ {len(image_results)} imagen(es) generada(s)",
                    size=12,
                    color=ft.Colors.GREEN,
                ),
                ft.Column(
                    controls=image_rows,
                    spacing=10,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                ),
            ],
            spacing=8,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        )
        self.page.update()

    async def show_generated_image(self, image_path, image_base64):
        """Método legacy - mostrar una sola imagen"""
        await self.show_generated_images([(image_path, image_base64)])


    async def show_error(self, error_message):
        """Mostrar error en la UI"""
        self.has_error = True
        self.content.content = ft.Column(
            controls=[
                ft.Text(self.label, weight=ft.FontWeight.BOLD),
                ft.Text(f"❌ {error_message}", size=12, color=ft.Colors.RED),
            ],
            spacing=8,
        )
        self.page.update()
        self.page.run_task(self.remove_from_parent)

    async def complete_task(self):
        """Completar la tarea y notificar al queue"""
        self.working = False

        # NO remover de la UI - mantener para mostrar la imagen
        # La tarea permanece visible con la imagen generada

        # Notificar al queue que la tarea terminó
        # self.task_queue.task_completed(self)
        self.page.update()

    async def start(self):
        self.working = True
        self.queue_position = 0
        self.monitoring_progress = False

        # Crear elementos de UI
        self.progress_ring = ft.ProgressRing(value=0)
        self.status_text = ft.Text(
            "Iniciando generación...", size=12, color=ft.Colors.BLUE
        )

        # Cambiar a anillo de progreso con texto de estado
        self.content.content = ft.Row(
            controls=[
                ft.Column(
                    controls=[
                        ft.Text(self.label, weight=ft.FontWeight.BOLD),
                        self.status_text,
                    ],
                    spacing=2,
                    expand=True,
                ),
                self.progress_ring,
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        self.page.update()

        # Iniciar generación en background
        await self.start_handler()

    def cancel(self, e):
        if (
            self.queue_position > 0
        ):  # Cancelar cualquier tarea que no esté procesando (posición 0)
            # Detener monitoreo si está activo
            self.working = False
            self.monitoring_progress = False

            self.task_queue.remove_task(self)
            if self.content in self.parent_container.controls:
                self.parent_container.controls.remove(self.content)
                self.page.update()

    def build(self):
        self.cancel_button = ft.IconButton(
            icon=ft.Icons.CANCEL,
            on_click=self.cancel,
            visible=False,
            tooltip="Cancelar tarea",
        )
        self.status_text = ft.Text("En cola", size=12, color=ft.Colors.GREY)

        # Add task type indicator with color
        task_type_colors = {
            "txt2img": ft.Colors.BLUE,
            "hr_upscale": ft.Colors.GREEN,
            "repeat": ft.Colors.ORANGE,
            "upscale": ft.Colors.PURPLE
        }

        task_type_color = task_type_colors.get(self.task_type, ft.Colors.GREY)
        self.task_type_text = ft.Text(f"[{self.task_type.upper()}]", size=10, color=task_type_color)

        container = ft.Container(
            content=ft.Row(
                controls=[
                    ft.Column(
                        controls=[
                            ft.Text(self.label, weight=ft.FontWeight.BOLD),
                            ft.Row([
                                self.status_text,
                                self.task_type_text
                            ], spacing=10),
                        ],
                        spacing=2,
                    ),
                    self.cancel_button,
                ],
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            ),
            padding=10,
            border=ft.border.all(1, ft.Colors.GREY_400),
            border_radius=8,
            margin=ft.margin.only(bottom=5),
            opacity=1,
        )

        # Agregar referencia al objeto task para fácil acceso
        container.data = self
        return container
