﻿import flet as ft
import os
import json


app_data_path = os.getenv("FLET_APP_STORAGE_DATA")
app_temp_path = os.getenv("FLET_APP_STORAGE_TEMP")

class ImageViewer:

    def load_image_json(self):
        """Load JSON metadata with robust error handling and automatic cleanup"""
        json_path = self.image_path.replace(".png", ".json")

        # Return None if JSON file doesn't exist
        if not os.path.exists(json_path):
            return None

        try:
            # Check if file is empty before attempting to parse
            if os.path.getsize(json_path) == 0:
                self._cleanup_corrupted_files(json_path)
                return None

            # Attempt to read and parse JSON
            with open(json_path, "r", encoding="utf-8") as f:
                content = f.read().strip()

                # Check if content is empty or whitespace only
                if not content:
                    self._cleanup_corrupted_files(json_path)
                    return None

                # Parse JSON content
                json_data = json.loads(content)

                # Validate that we have a dictionary with expected structure
                if not isinstance(json_data, dict):
                    self._cleanup_corrupted_files(json_path)
                    return None

                # Basic validation - ensure we have at least some expected keys
                if not any(key in json_data for key in ["parameters", "info", "image_filename"]):
                    self._cleanup_corrupted_files(json_path)
                    return None

                return json_data

        except (json.JSONDecodeError, ValueError):
            # JSON parsing failed - corrupted JSON
            self._cleanup_corrupted_files(json_path)
            return None

        except (FileNotFoundError, PermissionError, OSError, IOError):
            # File system errors - file might be locked, deleted, or permission issues
            return None

        except UnicodeDecodeError:
            # Encoding issues
            self._cleanup_corrupted_files(json_path)
            return None

        except Exception:
            # Catch any other unexpected errors to prevent app crashes
            self._cleanup_corrupted_files(json_path)
            return None

    def _cleanup_corrupted_files(self, json_path):
        """Safely delete corrupted JSON and corresponding PNG files"""
        try:
            # Delete the corrupted JSON file
            if os.path.exists(json_path):
                os.remove(json_path)

            # Delete the corresponding PNG file
            png_path = json_path.replace(".json", ".png")
            if os.path.exists(png_path):
                os.remove(png_path)

        except (PermissionError, OSError, FileNotFoundError):
            # If we can't delete files, silently continue
            # This prevents secondary errors from breaking the app
            pass
        except Exception:
            # Catch any other unexpected errors during cleanup
            pass

    def __init__(self, page: ft.Page, image_path, generate_callback):
        self.page = page
        self.image_path = image_path
        self.generate_callback = generate_callback
        self.image_json = self.load_image_json()
        self.content = self.build()


    def upscale(self, e):
        """Crear payload para upscale con HR (High Resolution)"""
        from a1111client import BasePayload

        # Check if JSON data is available
        if not self.image_json:
            # Create default upscale payload if no JSON metadata
            upscale_payload = BasePayload(
                prompt="upscaled image",
                n_iter=1,
                enable_hr=True,
                hr_upscaler="R-ESRGAN 4x+",
                hr_second_pass_steps=10,
                hr_scale=1.5,
                denoising_strength=0.7,
                width=512,
                height=512
            )
            prompt_preview = "upscaled"
        else:
            # Obtener parámetros originales
            params = self.image_json.get("parameters", {})

            # Extraer seed original del info JSON si está disponible
            original_seed = params.get("seed", -1)
            if original_seed == -1 and "info" in self.image_json:
                try:
                    import json
                    info_data = json.loads(self.image_json["info"])
                    original_seed = info_data.get("seed", -1)
                except (json.JSONDecodeError, KeyError, TypeError):
                    original_seed = -1

            # Crear payload para HR upscale manteniendo parámetros originales exactos
            upscale_payload = BasePayload(
                # Parámetros originales exactos
                prompt=params.get("prompt", ""),
                negative_prompt=params.get("negative_prompt", ""),
                width=params.get("width", 512),
                height=params.get("height", 512),
                sampler_name=params.get("sampler_name", "Euler a"),
                scheduler=params.get("scheduler", "Normal"),
                cfg_scale=params.get("cfg_scale", 7),
                steps=params.get("steps", 20),
                # Usar MISMO seed para consistencia con imagen base
                seed=original_seed,
                # HR upscale específico
                n_iter=1,
                enable_hr=True,
                hr_upscaler="R-ESRGAN 4x+",
                hr_second_pass_steps=15,
                hr_scale=2.0,
                denoising_strength=0.5,
                hr_resize_x=params.get("width", 512) * 2,
                hr_resize_y=params.get("height", 512) * 2,
                hr_cfg=params.get("cfg_scale", 7),
                hr_prompt="",
                hr_negative_prompt=""
            )

            prompt_preview = params.get("prompt", "")[:20]

        async def upscale_task():
            await self.generate_callback(f"Upscale: {prompt_preview}", upscale_payload, "hr_upscale")

        self.page.run_task(upscale_task)

    def repeat(self, e):
        """Repetir con los mismos parámetros originales pero seed=-1 para nuevas variaciones"""
        from a1111client import BasePayload

        # Check if JSON data is available
        if not self.image_json:
            # Create default payload if no JSON metadata
            payload = BasePayload(
                prompt="repeated image",
                n_iter=1,
                width=512,
                height=512,
                seed=-1  # Para nuevas variaciones
            )
            prompt_preview = "repeated"
        else:
            # Obtener parámetros originales
            params = self.image_json.get("parameters", {})

            # Obtener n_iter original para mantener el número de imágenes
            original_n_iter = params.get("n_iter", 1)

            # Crear payload con TODOS los parámetros originales exactos
            payload = BasePayload(
                prompt=params.get("prompt", ""),
                negative_prompt=params.get("negative_prompt", ""),
                width=params.get("width", 512),
                height=params.get("height", 512),
                sampler_name=params.get("sampler_name", "Euler a"),
                scheduler=params.get("scheduler", "Normal"),
                cfg_scale=params.get("cfg_scale", 7),
                steps=params.get("steps", 20),
                n_iter=original_n_iter,  # Mantener número original de imágenes
                seed=-1,  # Usar -1 para generar nuevas variaciones aleatorias
                # NO incluir parámetros HR para repeat normal
            )

            prompt_preview = params.get("prompt", "")[:20]

        async def repeat_task():
            await self.generate_callback(f"Repeat: {prompt_preview}", payload, "repeat")

        self.page.run_task(repeat_task)


    def get_alert_dialog(self):
        self.button_upscale = ft.ElevatedButton(
            "Upscale",
            on_click=self.upscale,
        )
        self.button_repeat = ft.ElevatedButton(
            "Repeat",
            on_click=self.repeat,
        )

        # Create content based on whether JSON data is available
        if self.image_json:
            params = self.image_json.get('parameters', {})
            content_controls = [
                ft.Image(
                    src=self.image_path,
                    width="inf",
                    fit=ft.ImageFit.CONTAIN,
                ),
                ft.Text(f"Image: {os.path.basename(self.image_path)}"),
                ft.Text(f"Prompt: {params.get('prompt', 'N/A')}"),
                ft.Text(f"Negative Prompt: {params.get('negative_prompt', 'N/A')}"),
                ft.Text(f"Steps: {params.get('steps', 'N/A')}"),
                ft.Text(f"CFG Scale: {params.get('cfg_scale', 'N/A')}"),
                ft.Text(f"Sampler: {params.get('sampler_name', 'N/A')}"),
                ft.Text(f"Scheduler: {params.get('scheduler', 'N/A')}"),
                self.button_upscale,
                self.button_repeat,
            ]
        else:
            content_controls = [
                ft.Text(f"Image: {os.path.basename(self.image_path)}"),
                ft.Text("⚠️ No metadata available", color=ft.Colors.ORANGE),
                ft.Text("JSON file was missing or corrupted"),
                ft.Text("Basic operations still available:"),
                self.button_upscale,
                self.button_repeat,
            ]

        self.dialog = ft.AlertDialog(
            title=ft.Text("Image Info"),
            content=ft.Column(
                content_controls,
                spacing=8,
                expand=True,
                scroll="auto"
            ),
            actions=[
                ft.TextButton("OK", on_click=lambda e: self.page.close(self.dialog)),
            ],
        )
        return self.dialog

    def open_dialog(self, e):
        self.page.open(self.get_alert_dialog())

    def build(self):
        return ft.Container(
            content=ft.Image(
                src=self.image_path,
                width=200,
                height=200,
                fit=ft.ImageFit.CONTAIN,
                border_radius=ft.border_radius.all(8),
            ),
            padding=10,
            border=ft.border.all(1, ft.Colors.GREY_400),
            border_radius=8,
            margin=ft.margin.only(bottom=5),
            on_click=self.open_dialog,
        )

